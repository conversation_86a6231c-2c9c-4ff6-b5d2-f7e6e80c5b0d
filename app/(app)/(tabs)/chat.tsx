import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
  Modal,
  Alert,
  LayoutAnimation,
  UIManager,
  Keyboard,
  KeyboardEvent,
  InteractionManager,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Palette, Image as ImageIcon, Camera, Menu, Pin, Edit2, Trash2, MessageSquarePlus, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import Colors from '@/constants/colors';
import type { ConversationIntent, Message } from '@/types';
import { generateTextSafe, getErrorMessage } from '@/lib/ai-client';
import { useChat } from '@/contexts/ChatContext';
import { useClients } from '@/contexts/ClientContext';
import ChatWelcomeHero from '@/components/chat/ChatWelcomeHero';
import ChatComposer from '@/components/chat/ChatComposer';
import MessageBubble from '@/components/chat/MessageBubble';
import PhotoGuidance from '@/components/PhotoGuidance';
import { showVisionSafetyError, isVisionSafetyError } from '@/lib/vision-safety-utils';
import { PHOTO_GUIDANCE_SHOW_DELAY_MS, PHOTO_GUIDANCE_DURATION_MS } from '@/constants/timing';
import { buildMainChatContext, sanitizeContext } from '@/lib/context-builders';
import { detectConversationIntent, describeIntent, formatMemoryForPrompt } from '@/lib/conversation-intelligence';
import type { IntentDetectionResult } from '@/lib/conversation-intelligence';
import { useImageAttachments } from '@/hooks/useImageAttachments';

const LEGACY_WELCOME_SNIPPET = 'asistente experto en coloración capilar';

// Scroll Anchoring Constants
const HEADER_BOTTOM_MARGIN = 12; // Spacing below sticky header for message anchoring
const FALLBACK_HEADER_HEIGHT = 112; // Approximate header height before dynamic measurement (insets.top + header content)
const KEYBOARD_LIST_BOTTOM_PADDING = 24; // Extra padding when keyboard is visible
const DEFAULT_LIST_BOTTOM_PADDING = 60; // Default bottom padding when keyboard is hidden

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

export default function ChatScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const flatListRef = useRef<FlatList>(null);
  const {
    conversations,
    currentConversation,
    currentConversationId,
    isLoading: conversationsLoading,
    startNewConversation,
    selectConversation,
    renameConversation,
    togglePinConversation,
    deleteConversation,
    addMessage,
    refreshConversationMemory,
  } = useChat();

  const { clients } = useClients();

  const [inputText, setInputText] = useState('');
  const {
    selectedImages,
    addImages,
    removeImageAt,
    clearImages,
    showAttachmentOptions,
    toggleAttachmentOptions,
    hideAttachmentOptions,
  } = useImageAttachments();
  const [isLoading, setIsLoading] = useState(false);
  const [showConversations, setShowConversations] = useState(false);
  const [editingConversationId, setEditingConversationId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [streamingMessage, setStreamingMessage] = useState<Message | null>(null);
  const streamingMessageIdRef = useRef<string | null>(null);
  const [showPhotoGuidance, setShowPhotoGuidance] = useState(false);
  const [hasSeenPhotoTip, setHasSeenPhotoTip] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const shouldAutoScrollRef = useRef(false);
  const autoScrollAnimatedRef = useRef(true);
  const hasInitialLayoutRef = useRef(false);
  const previousConversationIdRef = useRef<string | null>(null);
  const streamingAnchoredRef = useRef(false);
  const pendingMessageAnchorRef = useRef<{ id: string; animated: boolean; attempts: number } | null>(null);
  const displayMessagesRef = useRef<Message[]>([]);

  // Fix #6: AbortController to cancel AI requests on unmount (memory leak prevention)
  const abortControllerRef = useRef<AbortController | null>(null);

  const messages = useMemo(() => currentConversation?.messages || [], [currentConversation]);
  const hasUserMessages = useMemo(
    () => messages.some(message => message.role === 'user'),
    [messages]
  );
  const visibleMessages = useMemo(() => {
    if (hasUserMessages) {
      return messages;
    }
    return messages.filter(message => {
      if (message.role !== 'assistant') {
        return true;
      }
      if (!message.content) {
        return !!message.images?.length;
      }
      return !message.content.toLowerCase().includes(LEGACY_WELCOME_SNIPPET);
    });
  }, [messages, hasUserMessages]);
  const displayMessages = useMemo(
    () => (streamingMessage ? [...visibleMessages, streamingMessage] : visibleMessages),
    [visibleMessages, streamingMessage]
  );
  const shouldShowWelcomeHero = !hasUserMessages && visibleMessages.length === 0 && !streamingMessage;
  useEffect(() => {
    displayMessagesRef.current = displayMessages;
  }, [displayMessages]);
  const keyboardHeightRef = useRef(0);
  const headerHeightRef = useRef(0);

  /**
   * Scroll Anchoring Strategy:
   *
   * When a user sends a message, we want to keep it visible by "anchoring" it to a consistent
   * position on screen (just below the sticky header). This prevents messages from being hidden
   * behind the keyboard or scrolled off-screen.
   *
   * Flow:
   * 1. User sends message → Mark message ID as "pending anchor" with retry counter
   * 2. Message renders → FlatList triggers onLayout callback for message
   * 3. Calculate target scroll position using header height + margin
   * 4. Attempt scrollToIndex with the calculated offset
   * 5. If scrollToIndex fails (common with FlatList before measurements complete):
   *    - Retry up to 6 times (handles measurement timing issues)
   *    - Fall back to scrollToOffset if all retries fail (onScrollToIndexFailed)
   * 6. Clear pending anchor when successful or max attempts reached
   *
   * Why this approach?
   * - FlatList.scrollToIndex requires item measurements to be complete
   * - Item measurements happen asynchronously during render
   * - Retry logic handles race conditions gracefully
   * - Fallback ensures scrolling always works, even if imprecise
   */
  const getAnchorOffset = useCallback(() => {
    const measured = headerHeightRef.current;
    if (measured > 0) {
      return measured + HEADER_BOTTOM_MARGIN;
    }
    return insets.top + FALLBACK_HEADER_HEIGHT;
  }, [insets.top]);
  const requestScrollToEnd = useCallback((animated = true) => {
    shouldAutoScrollRef.current = true;
    autoScrollAnimatedRef.current = animated;
  }, []);
  const anchorToMessage = useCallback(
    (messageId: string, animated: boolean) => {
      const messages = displayMessagesRef.current;
      const index = messages.findIndex(msg => msg.id === messageId);

      if (index === -1) {
        return false;
      }

      const anchorOffset = getAnchorOffset();
      try {
        if (__DEV__) {
          console.log('[ChatScroll] scrollToIndex', {
            messageId,
            index,
            anchorOffset,
            animated,
          });
        }
        flatListRef.current?.scrollToIndex({
          index,
          animated,
          viewPosition: 0,
          viewOffset: anchorOffset,
        });
        return true;
      } catch (error) {
        if (__DEV__) {
          console.log('[ChatScroll] scrollToIndex failed', {
            messageId,
            index,
            anchorOffset,
            error: String(error),
          });
        }
        return false;
      }
    },
    [getAnchorOffset]
  );
  const tryAnchorPendingMessage = useCallback(() => {
    const pending = pendingMessageAnchorRef.current;
    if (!pending) return false;
    const anchored = anchorToMessage(pending.id, pending.animated);
    if (anchored) {
      pendingMessageAnchorRef.current = null;
      return true;
    }
    const nextAttempts = pending.attempts + 1;
    if (nextAttempts >= 6) {
      pendingMessageAnchorRef.current = null;
    } else {
      pendingMessageAnchorRef.current = { ...pending, attempts: nextAttempts };
    }
    return false;
  }, [anchorToMessage]);

  useEffect(() => {
    // Disable all automatic anchoring to prevent interference with manual scrollToIndex
    // The new simple scroll system handles positioning directly in sendMessage
  }, [streamingMessage, displayMessages]);

  // Cleanup streaming state and abort AI requests on unmount (Fix #6: memory leak prevention)
  useEffect(() => {
    const showListener = Keyboard.addListener('keyboardDidShow', (event: KeyboardEvent) => {
      keyboardHeightRef.current = event.endCoordinates?.height || 0;
      setKeyboardVisible(true);
    });
    const hideListener = Keyboard.addListener('keyboardDidHide', () => {
      keyboardHeightRef.current = 0;
      setKeyboardVisible(false);
    });

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, [requestScrollToEnd]);

  useEffect(() => {
    return () => {
      if (streamingMessageIdRef.current) {
        console.log('[Chat] Cleaning up streaming state on unmount');
        streamingMessageIdRef.current = null;
      }
      // Abort any ongoing AI requests
      if (abortControllerRef.current) {
        console.log('[Chat] Aborting ongoing AI request on unmount');
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (currentConversationId && previousConversationIdRef.current !== currentConversationId) {
      previousConversationIdRef.current = currentConversationId;
      hideAttachmentOptions();
      hasInitialLayoutRef.current = false;
      pendingMessageAnchorRef.current = null;
      requestScrollToEnd(false);
    }
  }, [currentConversationId, requestScrollToEnd, hideAttachmentOptions]);

  const composerBaseHeight = 82;
  const listBottomPadding = keyboardVisible
    ? keyboardHeightRef.current + composerBaseHeight + KEYBOARD_LIST_BOTTOM_PADDING
    : insets.bottom + composerBaseHeight + DEFAULT_LIST_BOTTOM_PADDING;
  const inputBottomPadding = keyboardVisible ? 12 : insets.bottom + 20;
  const formulaButtonMargin = keyboardVisible ? 12 : 16;

  // Show photo guidance tip on first visit (3 seconds)
  useEffect(() => {
    if (!hasSeenPhotoTip && !conversationsLoading) {
      const timer = setTimeout(() => {
        setShowPhotoGuidance(true);
      }, PHOTO_GUIDANCE_SHOW_DELAY_MS);

      const hideTimer = setTimeout(() => {
        setShowPhotoGuidance(false);
        setHasSeenPhotoTip(true);
      }, PHOTO_GUIDANCE_DURATION_MS);

      return () => {
        clearTimeout(timer);
        clearTimeout(hideTimer);
      };
    }
  }, [hasSeenPhotoTip, conversationsLoading]);

  const pickImage = async () => {
    hideAttachmentOptions();
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      quality: 0.8,
      allowsMultipleSelection: true,
      selectionLimit: 6,
    });

    if (!result.canceled && result.assets.length > 0) {
      const newImages = result.assets.map(asset => asset.uri);
      addImages(newImages);
    }
  };

  const takePhoto = async () => {
    // Request camera permissions
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permiso necesario', 'Se necesita permiso para acceder a la cámara');
      return;
    }
    hideAttachmentOptions();

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      const newImage = result.assets[0].uri;
      addImages([newImage]);
    }
  };

  // Memoize system prompt - only rebuild when clients change
  const enhancedSystemPrompt = useMemo(() => {
    // Contexto de clientes recientes (últimos 10)
    const clientContext = clients
      .slice(-10)
      .map(c => {
        const lastVisitStr = c.lastVisit
          ? new Date(c.lastVisit).toLocaleDateString('es-ES', { month: 'short', day: 'numeric' })
          : 'no registrada';
        const allergies = c.knownAllergies ? ` ⚠️ ALERGIAS: ${c.knownAllergies}` : '';
        return `• ${c.name}: Última visita ${lastVisitStr}${allergies}`;
      })
      .join('\n');

    return `Eres un experto en coloración capilar con más de 15 años de experiencia. Asistes a coloristas, estilistas y dueños de salón con diagnósticos y fórmulas altamente profesionales.

## Contexto del salón
${clientContext || 'No hay clientes registrados aún.'}

## Estilo de comunicación
- Profesional, empático y claro.
- Explica siempre el "por qué" de cada decisión.
- Prioriza términos técnicos comprensibles y recomendaciones accionables.
- Concluye con próximos pasos concretos.

## Formato preferido
### Consultas generales
1. **Resumen rápido** en una frase.
2. **Diagnóstico o análisis** con subtítulos claros.
3. **Recomendaciones accionables** en viñetas.
4. **Tips profesionales** opcionales.

### Análisis de imagen
- Usa el encabezado \`## Análisis de imagen\`.
- Incluye viñetas para raíces, medios, puntas, canas, condición y tonos dominantes.
- Añade \`⚠️ Riesgos\` si detectas alertas.

### Fórmulas y planes técnicos
- Encabezado \`## Fórmula sugerida\`.
- Bloque \`### Mezcla\` con cantidades exactas.
- Bloque \`### Aplicación\` con pasos enumerados.
- Bloque \`### Notas críticas\` para advertencias.
- Termina con \`💡 Tip pro\` cuando aporte valor.

## Reglas de seguridad
1. Pregunta por alergias si no aparecen en el historial.
2. Recomienda patch test para nuevos productos.
3. Advierte sobre riesgos de aclaración extrema.
4. Valida datos si mencionan clientes o servicios previos.

Responde siempre en español neutral, directo y educativo.`;
  }, [clients]); // Only rebuild when clients array changes

  const sendMessage = async () => {
    hideAttachmentOptions();
    if (!inputText.trim() && selectedImages.length === 0) return;
    if (!currentConversationId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputText.trim(),
      timestamp: new Date(),
      images: selectedImages.length > 0 ? [...selectedImages] : undefined,
    };

    const currentInput = inputText.trim();
    const hasImages = selectedImages.length > 0;

    // Guardar URIs locales ANTES de limpiar selectedImages
    // Vamos a pasarlas directamente a generateTextSafe() como lo hace step1
    const currentImageUris = hasImages ? [...selectedImages] : [];

    // 🎯 SIMPLE CLAUDE-LIKE BEHAVIOR
    // 1. Dismiss keyboard immediately for responsive feel
    Keyboard.dismiss();

    // 2. Add message to state (optimistic update)
    await addMessage(currentConversationId, userMessage);

    // 3. Clear input immediately for responsive UX
    setInputText('');
    clearImages();
    setIsLoading(true);

    // 4. Scroll to show new message at top (like Claude)
    setTimeout(() => {
      const messages = displayMessagesRef.current;
      if (messages.length > 0) {
        const lastMessageIndex = messages.length - 1;
        flatListRef.current?.scrollToIndex({
          index: lastMessageIndex,
          animated: true,
          viewPosition: 0, // Position at top of screen
          viewOffset: getAnchorOffset(), // Account for header
        });
      }
    }, 100); // Small delay to ensure message is rendered
    hideAttachmentOptions();

    let streamingEnabled = false;
    let streamedContent = '';
    const memorySnapshot = currentConversation?.memorySummary ?? null;
    let detectedIntent: ConversationIntent = 'otro';

    // Fix #6: Create AbortController for this AI request (memory leak prevention)
    abortControllerRef.current = new AbortController();

    try {
      let aiResponseContent = '';
      let intentResult: IntentDetectionResult | null = null;

      const productKeywords = /\b(marca|marcas|producto|productos|línea|líneas|gama|referencia|catálogo|salerm|wella|schwarzkopf|loreal|l'oreal|goldwell|redken|color|tinte|oxidante|revelador)\b/i;
      const isProductQuery = !hasImages && productKeywords.test(currentInput);

      const currentMessages = currentConversation?.messages || [];
      const messagesForIntent = currentMessages
        .slice(-8)
        .map((msg) => ({
          ...msg,
          content: msg.content || '',
        })) as Message[];

      try {
        intentResult = await detectConversationIntent({
          userMessage: currentInput,
          recentMessages: messagesForIntent,
          memory: memorySnapshot,
        });
        if (__DEV__) {
          console.log('[Chat] Intent detection result:', intentResult);
        }
      } catch (intentError) {
        console.warn('[Chat] Intent detection failed', intentError);
      }

      detectedIntent = intentResult?.intent ?? 'otro';
      const memorySection = formatMemoryForPrompt(memorySnapshot);
      const intentSection = describeIntent(detectedIntent);

      const rawContext = await buildMainChatContext({
        memory: memorySnapshot,
        intent: detectedIntent,
      });
      const appContext = sanitizeContext(rawContext);

      if (hasImages && currentImageUris.length > 0) {
        const baseVisionPrompt = `You are a professional hair colorist with 15+ years of experience. You analyze hair photos with technical precision for salon services.

CRITICAL CONTEXT: This is a professional hair salon analysis service. The client has explicitly consented to hair analysis for coloring services. This is similar to medical dermatology analysis - focusing on hair/scalp condition for treatment purposes.

YOUR ANALYSIS SCOPE:
- ANALYZE ONLY: Hair color, texture, condition, damage level, root regrowth, gray coverage, previous color treatments
- COMPLETELY IGNORE: Faces, people, personal identity, demographics, facial features, age, gender, ethnicity
- If a person appears in the photo, treat them as transparent - analyze ONLY the hair visible

This is professional salon service, not personal identification. Focus exclusively on hair characteristics needed for color formulation.

ANÁLISIS DE IMÁGENES - Cuando analices fotos de cabello, proporciona:

1. **Análisis de Color Actual**:
   - Nivel de color (escala 1-10: 1=negro, 10=rubio muy claro)
   - Tono dominante (cálido/frío/neutro)
   - Reflejos presentes (dorado/cobrizo/ceniza/rojizo/violeta)
   - Profundidad del color (opaco/brillante/translúcido)

2. **Evaluación de Canas**:
   - Porcentaje aproximado (0-25%, 25-50%, 50-75%, 75-100%)
   - Distribución (uniforme, concentradas en zonas específicas)
   - Textura diferenciada

3. **Estado del Cabello**:
   - Historial: virgen, teñido, con mechas, decolorado
   - Porosidad aparente (baja/media/alta)
   - Integridad de la cutícula
   - Diferencias entre zonas (raíces/medios/puntas)

4. **Recomendaciones Técnicas**:
   - Tratamientos previos necesarios
   - Técnica de aplicación recomendada
   - Tiempo de exposición aproximado
   - Cuidados post-coloración

Si recibes múltiples fotos, analiza todas las vistas y proporciona un diagnóstico completo. Sé específico con números de nivel, nomenclatura técnica y precauciones.`;

        const systemPrompt = `${baseVisionPrompt}\n\n${memorySection}\n\n${intentSection}\n\nRefuerza la continuidad usando el contexto recordado y evita repetir preguntas ya contestadas.`;

        const professionalContext = `[PROFESSIONAL HAIR SALON SERVICE]
Client Consent: ✓ Provided for hair color analysis
Service Type: Hair colorization consultation (similar to medical dermatology)
Analysis Scope: Hair condition, color level, damage assessment ONLY
Privacy: Ignore all personal/facial features - focus exclusively on hair

HAIR ANALYSIS REQUEST:\n`;
        const userPrompt = professionalContext + (currentInput || 'Please analyze the hair visible in these images. Provide a complete diagnosis of color level, condition, previous treatments, and technical recommendations for color formulation.');

        const conversationHistory = currentMessages
          .slice(-6)
          .filter(msg => (msg.role === 'assistant' || msg.role === 'user') && msg.content.trim().length > 0)
          .map((msg) => {
            if (msg.images && msg.images.length > 0) {
              return {
                role: msg.role as 'user' | 'assistant',
                content: `${msg.content} [imagen analizada previamente]`
              };
            }
            return {
              role: msg.role as 'user' | 'assistant',
              content: msg.content
            };
          });

        if (__DEV__) {
          console.log('[Chat] Conversation history length:', conversationHistory.length);
          console.log('[Chat] Vision intent detected:', detectedIntent, 'confidence:', intentResult?.confidence);
        }

        console.log('[Chat] Calling vision_analysis with', currentImageUris.length, 'images');

        aiResponseContent = await generateTextSafe({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
          ],
          maxRetries: 2,
          retryDelay: 1500,
          useCase: 'vision_analysis',
          imageUris: currentImageUris,
          conversationHistory: conversationHistory.length > 0 ? conversationHistory : undefined,
          requestTimeout: 120000,
          appContext: appContext || undefined,
          signal: abortControllerRef.current.signal,
        });
        console.log('[Chat] Vision analysis response received, length:', aiResponseContent.length);
      } else if (isProductQuery) {
        const brandMatch = currentInput.match(/\b(salerm|wella|schwarzkopf|loreal|l'oreal|goldwell|redken)\b/i);
        const brand = brandMatch ? brandMatch[1] : undefined;

        console.log(`[Chat] Product query detected for brand: ${brand || 'generic'}`);

        try {
          const { searchProducts } = await import('@/lib/ai-client');
          const result = await searchProducts(
            brand || '',
            undefined,
            currentInput
          );

          aiResponseContent = result.content;

          if (result.citations && result.citations.length > 0) {
            aiResponseContent += '\n\n**Fuentes:**\n';
            result.citations.forEach((citation: string, index: number) => {
              aiResponseContent += `${index + 1}. ${citation}\n`;
            });
          }
        } catch (error: any) {
          console.error('[Chat] Product search failed, falling back to chat:', error);
          console.error('[Chat] Error details:', {
            message: error?.message,
            name: error?.name,
            stack: error?.stack?.split('\n')[0]
          });

          const fallbackPrompt = `Eres un experto en coloración capilar profesional.

IMPORTANTE: Si te preguntan sobre productos o marcas específicas y NO tienes información precisa y actualizada, debes:
1. Ser honesto y decir que no tienes información actualizada de ese producto específico
2. Recomendar al usuario verificar en la web oficial de la marca
3. Ofrecer información general sobre el tipo de producto

NO inventes nombres de productos o líneas que no conozcas con certeza.`;

          const conversationHistory = currentMessages
            .slice(-10)
            .filter(msg => (msg.role === 'assistant' || msg.role === 'user') && msg.content.trim().length > 0)
            .map((msg) => {
              if (msg.images && msg.images.length > 0) {
                return {
                  role: msg.role as 'user' | 'assistant',
                  content: `${msg.content} [imagen analizada previamente]`
                };
              }
              return {
                role: msg.role as 'user' | 'assistant',
                content: msg.content
              };
            });

          aiResponseContent = await generateTextSafe({
            messages: [
              { role: 'system', content: `${fallbackPrompt}\n\n${memorySection}\n\n${intentSection}` },
              { role: 'user', content: currentInput },
            ],
            maxRetries: 2,
            retryDelay: 1500,
            useCase: 'chat',
            conversationHistory: conversationHistory.length > 0 ? conversationHistory : undefined,
            appContext: appContext || undefined,
            signal: abortControllerRef.current.signal,
          });
        }
      } else {
        const systemPrompt = `${enhancedSystemPrompt}\n\n${memorySection}\n\n${intentSection}\n\nCuando falte información crítica, pídela antes de recomendar. Usa la memoria para mantener continuidad y evita repetir preguntas.`;

        const conversationHistory = currentMessages
          .slice(-10)
          .filter(msg => (msg.role === 'assistant' || msg.role === 'user') && msg.content.trim().length > 0)
          .map((msg) => {
            if (msg.images && msg.images.length > 0) {
              return {
                role: msg.role as 'user' | 'assistant',
                content: `${msg.content} [imagen analizada previamente]`
              };
            }
            return {
              role: msg.role as 'user' | 'assistant',
              content: msg.content
            };
          });

        streamingEnabled = true;
        const tempMessageId = `stream-${Date.now()}`;
        streamingMessageIdRef.current = tempMessageId;
        setStreamingMessage({
          id: tempMessageId,
          role: 'assistant',
          content: '',
          timestamp: new Date(),
        });

        aiResponseContent = await generateTextSafe({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: currentInput },
          ],
          maxRetries: 2,
          retryDelay: 1500,
          useCase: 'chat',
          conversationHistory: conversationHistory.length > 0 ? conversationHistory : undefined,
          stream: true,
          requestTimeout: 45000,
          appContext: appContext || undefined,
          signal: abortControllerRef.current.signal,
          onStreamResult: (event) => {
            if ((event.type === 'token' || event.type === 'done') && typeof event.text === 'string') {
              streamedContent = event.text;
              setStreamingMessage(prev =>
                prev && prev.id === tempMessageId ? { ...prev, content: event.text } : prev
              );
            } else if (event.type === 'error' && event.error) {
              streamedContent = event.error;
              setStreamingMessage(prev =>
                prev && prev.id === tempMessageId ? { ...prev, content: event.error } : prev
              );
            }
          },
        });

        if (streamedContent) {
          aiResponseContent = streamedContent;
        }
      }

      console.log('[Chat] Creating AI response message with content length:', aiResponseContent.length);

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: aiResponseContent,
        timestamp: new Date(),
      };

      console.log('[Chat] About to add AI response to conversation:', currentConversationId);
      await addMessage(currentConversationId, aiResponse);
      console.log('[Chat] AI response added successfully');

      const summaryMessages = [...currentMessages, userMessage, aiResponse];
      void refreshConversationMemory(currentConversationId, detectedIntent, summaryMessages);

      if (streamingEnabled) {
        setStreamingMessage(null);
        streamingMessageIdRef.current = null;
      }
    } catch (error: any) {
      console.error('Error al obtener respuesta de IA:', error);

      // Detect vision safety rejection
      if (isVisionSafetyError(error)) {
        showVisionSafetyError(
          () => {
            clearImages();
            setShowPhotoGuidance(true);
          },
          () => sendMessage(),
          { showGuidanceTip: true }
        );
        setIsLoading(false);
        return;
      }

      // Handle mobile network errors (timeout, connection issues)
      if (error.name === 'AbortError' || error.message?.includes('timeout') || error.message?.includes('network')) {
        const networkErrorMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '⚠️ **Conexión lenta o interrumpida**\n\nLa solicitud tardó demasiado tiempo. Esto suele ocurrir en conexiones móviles lentas.\n\n**Sugerencias:**\n- Verifica tu conexión a internet\n- Intenta con WiFi si es posible\n- Reduce el tamaño del mensaje o imágenes\n- Vuelve a intentarlo',
          timestamp: new Date(),
        };

        if (streamingMessageIdRef.current) {
          setStreamingMessage(null);
          streamingMessageIdRef.current = null;
        }

        await addMessage(currentConversationId, networkErrorMessage);
        setIsLoading(false);
        return;
      }

      // Get user-friendly error message
      const errorMessage = getErrorMessage(error);

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date(),
      };

      if (streamingMessageIdRef.current) {
        setStreamingMessage(null);
        streamingMessageIdRef.current = null;
      }

      await addMessage(currentConversationId, errorResponse);
    } finally {
      setIsLoading(false);
      // Fix #6: Cleanup AbortController after request completes
      abortControllerRef.current = null;
    }
  };

  const handleNewConversation = async () => {
    await startNewConversation();
    setShowConversations(false);
  };

  const handleSelectConversation = (id: string) => {
    selectConversation(id);
    setShowConversations(false);
  };

  const handleStartEdit = (id: string, currentTitle: string) => {
    setEditingConversationId(id);
    setEditingTitle(currentTitle);
  };

  const handleSaveEdit = async () => {
    if (editingConversationId && editingTitle.trim()) {
      await renameConversation(editingConversationId, editingTitle.trim());
    }
    setEditingConversationId(null);
    setEditingTitle('');
  };

  const handleDeleteConversation = (id: string, title: string) => {
    Alert.alert(
      'Eliminar conversación',
      `¿Estás seguro de que quieres eliminar "${title}"?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => await deleteConversation(id),
        },
      ]
    );
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <MessageBubble
      message={item}
      variant="chat"
      onLayout={() => {
        // Disable anchoring system to prevent interference with manual scrollToIndex
      }}
      onImageError={(uri, error) => {
        console.warn('[Chat] Error cargando imagen:', uri, error);
      }}
    />
  );

  const renderConversationItem = ({ item }: { item: typeof conversations[0] }) => {
    const isEditing = editingConversationId === item.id;
    const isSelected = currentConversationId === item.id;

    return (
      <View style={[styles.conversationItem, isSelected && styles.conversationItemSelected]}>
        {isEditing ? (
          <View style={styles.editingContainer}>
            <TextInput
              style={styles.editInput}
              value={editingTitle}
              onChangeText={setEditingTitle}
              autoFocus
              onSubmitEditing={handleSaveEdit}
            />
            <TouchableOpacity onPress={handleSaveEdit} style={styles.editSaveButton}>
              <Text style={styles.editSaveText}>✓</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <TouchableOpacity
              style={styles.conversationContent}
              onPress={() => handleSelectConversation(item.id)}
            >
              <View style={styles.conversationHeader}>
                <Text style={styles.conversationTitle} numberOfLines={1}>
                  {item.title}
                </Text>
                {item.isPinned && <Pin size={14} color={Colors.light.primary} fill={Colors.light.primary} />}
              </View>
              <Text style={styles.conversationDate}>
                {item.updatedAt.toLocaleDateString('es-ES', { 
                  day: 'numeric',
                  month: 'short',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </TouchableOpacity>
            <View style={styles.conversationActions}>
              <TouchableOpacity
                onPress={async () => await togglePinConversation(item.id)}
                style={styles.actionButton}
              >
                <Pin 
                  size={18} 
                  color={item.isPinned ? Colors.light.primary : Colors.light.textLight}
                  fill={item.isPinned ? Colors.light.primary : 'transparent'}
                />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleStartEdit(item.id, item.title)}
                style={styles.actionButton}
              >
                <Edit2 size={18} color={Colors.light.textLight} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleDeleteConversation(item.id, item.title)}
                style={styles.actionButton}
              >
                <Trash2 size={18} color={Colors.light.error} />
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (!isLoading || streamingMessage) return null;

    return (
      <View style={styles.typingContainer}>
        <View style={styles.typingBubble}>
          <ActivityIndicator size="small" color={Colors.light.primary} />
          <Text style={styles.typingText}>Salonier está formulando la respuesta...</Text>
        </View>
      </View>
    );
  };

  if (conversationsLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        testID="chat-message-list"
        ref={flatListRef}
        data={displayMessages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.messagesList,
          {
            paddingBottom: listBottomPadding,
          },
        ]}
        ListHeaderComponent={
          <View style={styles.headerContainer}>
            <View
              style={[styles.header, { paddingTop: insets.top + 16 }]}
              onLayout={({ nativeEvent }) => {
                headerHeightRef.current = nativeEvent.layout.height;
              }}
            >
              <TouchableOpacity
                style={styles.menuButton}
                onPress={() => setShowConversations(true)}
                hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
              >
                <Menu color={Colors.light.text} size={24} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Salonier AI</Text>
            </View>
            {shouldShowWelcomeHero ? (
              <View style={styles.welcomeHeader}>
                <ChatWelcomeHero />
              </View>
            ) : (
              <View style={styles.listTopSpacer} />
            )}
          </View>
        }
        stickyHeaderIndices={[0]}
        onContentSizeChange={() => {
          // Disable all automatic scrolling to prevent interference with manual scrollToIndex
          // The new simple scroll system handles positioning directly in sendMessage
        }}
        onLayout={() => {
          if (!hasInitialLayoutRef.current) {
            hasInitialLayoutRef.current = true;
            requestScrollToEnd(false);
          }
        }}
        ListFooterComponent={renderTypingIndicator}
        // Performance optimizations (Fix #4: 30-40fps → 60fps)
        removeClippedSubviews={Platform.OS === 'android'} // Memory optimization (Android only, iOS has issues with chat)
        maxToRenderPerBatch={10} // Render 10 items per batch for smooth scrolling
        windowSize={21} // Render 10 screens above + 1 visible + 10 below
        initialNumToRender={10} // Initial render of 10 messages (typical chat view)
        updateCellsBatchingPeriod={50} // Batch updates every 50ms (balance between responsiveness and performance)
        onScrollToIndexFailed={({ index, highestMeasuredFrameIndex, averageItemLength }) => {
          if (__DEV__) {
            console.log('[ChatScroll] onScrollToIndexFailed', {
              index,
              highestMeasuredFrameIndex,
              averageItemLength,
            });
          }

          const anchorOffset = getAnchorOffset();
          const approxSize = averageItemLength || 200;
          const fallbackOffset = Math.max(index * approxSize - anchorOffset, 0);

          requestAnimationFrame(() => {
            flatListRef.current?.scrollToOffset({ offset: fallbackOffset, animated: false });
            requestAnimationFrame(() => {
              tryAnchorPendingMessage();
            });
          });
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 16 : 0}
      >
        <View style={[styles.inputContainer, { paddingBottom: inputBottomPadding }]}>
          <TouchableOpacity
            style={[styles.formulaButton, { marginBottom: formulaButtonMargin }]}
            onPress={() => router.push('/formula/step0')}
          >
            <Palette color={Colors.light.background} size={18} />
            <Text style={styles.formulaButtonText}>Crear Fórmula</Text>
          </TouchableOpacity>

          {showPhotoGuidance && (
            <View style={styles.photoGuidanceContainer}>
              <PhotoGuidance compact />
            </View>
          )}

          {selectedImages.length > 0 && (
            <View style={styles.imagesPreviewContainer}>
              {selectedImages.map((imgUri, index) => (
                <View
                  key={index}
                  style={[
                    styles.imagePreviewWrapper,
                    selectedImages.length === 1 && styles.singleImagePreviewWrapper
                  ]}
                >
                  <Image
                    source={{ uri: imgUri }}
                    style={[
                      styles.imagePreview,
                      selectedImages.length === 1 && styles.singleImagePreview
                    ]}
                  />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => removeImageAt(index)}
                  >
                    <Text style={styles.removeImageText}>✕</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          {showAttachmentOptions && (
            <View style={styles.attachmentsCard}>
              <View style={styles.attachmentsHeader}>
                <Text style={styles.attachmentsTitle}>Añadir al chat</Text>
                <TouchableOpacity
                  accessibilityRole="button"
                  onPress={hideAttachmentOptions}
                  style={styles.attachmentsClose}
                >
                  <X color={Colors.light.textSecondary} size={18} />
                </TouchableOpacity>
              </View>
              <View style={styles.attachmentRow}>
                <TouchableOpacity
                  style={styles.attachmentOption}
                  onPress={takePhoto}
                >
                  <View style={styles.attachmentIconBadge}>
                    <Camera color={Colors.light.primary} size={18} />
                  </View>
                  <Text style={styles.attachmentLabel}>Cámara</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.attachmentOption}
                  onPress={pickImage}
                >
                  <View style={styles.attachmentIconBadge}>
                    <ImageIcon color={Colors.light.primary} size={18} />
                  </View>
                  <Text style={styles.attachmentLabel}>Fotos</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          <ChatComposer
            variant="chat"
            value={inputText}
            placeholder="Escribe tu consulta..."
            onChangeText={setInputText}
            onSend={sendMessage}
            disabled={(!inputText.trim() && selectedImages.length === 0) || isLoading}
            onPressAttachment={toggleAttachmentOptions}
            onLongPressAttachment={() => setShowPhotoGuidance(prev => !prev)}
            onFocusInput={hideAttachmentOptions}
            sendButtonTestID="chat-send-button"
          />
        </View>
      </KeyboardAvoidingView>

      <Modal
        visible={showConversations}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setShowConversations(false)}
      >
        <View style={[styles.modalContainer, { paddingTop: insets.top }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Conversaciones</Text>
            <TouchableOpacity
              onPress={() => setShowConversations(false)}
              style={styles.closeButton}
            >
              <X color={Colors.light.text} size={24} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.newConversationButton}
            onPress={handleNewConversation}
          >
            <MessageSquarePlus color={Colors.light.primary} size={22} />
            <Text style={styles.newConversationText}>Nueva conversación</Text>
          </TouchableOpacity>

          <FlatList
            data={conversations}
            renderItem={renderConversationItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.conversationsList}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: Colors.light.background,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    backgroundColor: Colors.light.background,
    zIndex: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '800' as const,
    color: Colors.light.text,
    letterSpacing: -0.5,
    marginLeft: 12,
    flexShrink: 1,
  },
  menuButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    marginRight: 12,
    borderRadius: 12,
  },
  listTopSpacer: {
    height: 20,
  },
  messagesList: {
    paddingHorizontal: 20,
  },
  welcomeHeader: {
    marginBottom: 24,
  },
  inputContainer: {
    paddingHorizontal: 20,
    paddingTop: 12,
    backgroundColor: 'transparent',
  },
  photoGuidanceContainer: {
    marginBottom: 12,
  },
  imagesPreviewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  imagePreviewWrapper: {
    position: 'relative',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
    overflow: 'hidden',
  },
  singleImagePreviewWrapper: {
    // Contenedor para imagen única
  },
  imagePreview: {
    // Preview para múltiples imágenes (grilla) - tipo Claude
    width: 120,
    height: 120,
  },
  singleImagePreview: {
    // Preview para imagen única - tipo Claude (compacto)
    width: 140,
    height: 140,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.light.text,
    width: 26,
    height: 26,
    borderRadius: 13,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  removeImageText: {
    color: Colors.light.background,
    fontSize: 15,
    fontWeight: '700' as const,
  },
  attachmentsCard: {
    alignSelf: 'stretch',
    marginBottom: 16,
    padding: 16,
    borderRadius: 28,
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 4,
    gap: 16,
  },
  attachmentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  attachmentsTitle: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
    letterSpacing: -0.1,
  },
  attachmentsClose: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
  },
  attachmentRow: {
    flexDirection: 'row',
    gap: 12,
  },
  attachmentOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 20,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  attachmentIconBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.background,
  },
  attachmentLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: Colors.light.textSecondary,
  },
  formulaButton: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: 18,
    paddingVertical: 11,
    backgroundColor: Colors.light.primary,
    borderRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  formulaButtonText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.background,
    letterSpacing: -0.1,
  },
  typingContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  typingBubble: {
    backgroundColor: Colors.light.aiMessage,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    maxWidth: '80%',
    shadowColor: Colors.light.primary,
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  typingText: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  closeButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  newConversationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.light.backgroundSecondary,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 12,
  },
  newConversationText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  conversationsList: {
    paddingHorizontal: 16,
  },
  conversationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  conversationItemSelected: {
    backgroundColor: Colors.light.backgroundSecondary,
  },
  conversationContent: {
    flex: 1,
    gap: 4,
  },
  conversationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
    flex: 1,
  },
  conversationDate: {
    fontSize: 13,
    color: Colors.light.textLight,
  },
  conversationActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: 4,
  },
  editingContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  editInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  editSaveButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.primary,
    borderRadius: 18,
  },
  editSaveText: {
    fontSize: 18,
    color: Colors.light.background,
    fontWeight: '700' as const,
  },
});
